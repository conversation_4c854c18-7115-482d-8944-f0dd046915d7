import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Product } from '@/models/Product';

// GET - Check stock availability for a specific product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase();
    
    const productId = params.id;
    
    // Find product and get only inventory information
    const product = await Product.findById(productId)
      .select('name inventory')
      .lean();
    
    if (!product) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      );
    }
    
    const inventory = product.inventory || { inStock: 0, lowStockThreshold: 5 };
    
    // Determine stock status
    let status = 'out_of_stock';
    let available = true;
    
    if (inventory.inStock > inventory.lowStockThreshold) {
      status = 'in_stock';
    } else if (inventory.inStock > 0) {
      status = 'low_stock';
    } else {
      status = 'out_of_stock';
      available = false;
    }
    
    return NextResponse.json({
      success: true,
      productId,
      productName: product.name,
      inventory: {
        inStock: inventory.inStock,
        lowStockThreshold: inventory.lowStockThreshold,
        status,
        available
      }
    });
  } catch (error) {
    console.error('Error checking stock:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to check stock' },
      { status: 500 }
    );
  }
}

// POST - Check stock availability for multiple products (for cart validation)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase();
    
    const { quantity } = await request.json();
    const productId = params.id;
    
    if (!quantity || quantity < 1) {
      return NextResponse.json(
        { success: false, message: 'Invalid quantity' },
        { status: 400 }
      );
    }
    
    // Find product and get inventory information
    const product = await Product.findById(productId)
      .select('name inventory')
      .lean();
    
    if (!product) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      );
    }
    
    const inventory = product.inventory || { inStock: 0, lowStockThreshold: 5 };
    const available = inventory.inStock >= quantity;
    
    return NextResponse.json({
      success: true,
      productId,
      productName: product.name,
      requestedQuantity: quantity,
      availableStock: inventory.inStock,
      available,
      message: available 
        ? `${quantity} items available` 
        : `Only ${inventory.inStock} items available, requested ${quantity}`
    });
  } catch (error) {
    console.error('Error validating stock:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to validate stock' },
      { status: 500 }
    );
  }
}
