'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { 
  ChevronDown, 
  ChevronUp, 
  ChevronRight, 
  Search, 
  SlidersHorizontal, 
  X, 
  Filter,
  ShoppingCart
} from 'lucide-react';
import ProductCard from '@/components/ProductCard';
import NewSidebar from '@/components/NewSidebar';
import { useLanguage } from '@/contexts/LanguageContext';
import { useProductStore } from '@/store/useProductStore';

// Types from parent component
type Subcategory = {
  _id: string;
  name: string;
};

type Category = {
  _id: string;
  name: string;
  subcategories: Subcategory[];
};

type CategoryObj = {
  _id: string;
  name: string;
};

type Product = {
  _id: string;
  name: string;
  price: number;
  images: string[];
  videos?: {
    url: string;
    thumbnail?: string;
  }[];
  category: string | CategoryObj;
  subcategory?: string | CategoryObj;
  description: {
    en: string;
    fr?: string;
    it?: string;
  };
  shape?: string;
  weight?: number;
  inventory?: {
    inStock: number;
    lowStockThreshold: number;
  };
  [key: string]: any;
};

type SortOption = {
  label: string;
  value: string;
};

// Modify state types for multiple selections
type ShopClientProps = {
  initialProducts: Product[];
  initialCategories: Category[];
};

// Animation styles
const animationStyles = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(100%);
    }
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out forwards;
  }
  
  .animate-slide-out-right {
    animation: slideOutRight 0.3s ease-in forwards;
  }
`;

export default function ShopClientComponent({ initialProducts, initialCategories }: ShopClientProps) {
  const searchParams = useSearchParams();
  const { translations, translateCategoryName, translateSubcategoryName } = useLanguage();
  const { products: prefetchedProducts } = useProductStore();

  // States - keep exactly the same
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states - keep exactly the same
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedSubcategories, setSelectedSubcategories] = useState<string[]>([]);
  const [selectedShape, setSelectedShape] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  
  // UI states - keep exactly the same
  const [sortOption, setSortOption] = useState<string>('newest');
  const [isMobile, setIsMobile] = useState(false);
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);
  const [isFilterDrawerClosing, setIsFilterDrawerClosing] = useState(false);
  const [expandedDrawerSections, setExpandedDrawerSections] = useState<Record<string, boolean>>({
    gemstoneVariety: false,
    shape: false
  });
  const filterDrawerRef = useRef<HTMLDivElement>(null);

  // Handle drawer closing with animation - keep exactly the same
  const closeFilterDrawer = () => {
    setIsFilterDrawerClosing(true);
    // Wait for animation to complete before actually closing
    setTimeout(() => {
      setIsFilterDrawerOpen(false);
      setIsFilterDrawerClosing(false);
    }, 300); // Match animation duration
  };
  
  // Close filter drawer when clicking outside - keep exactly the same
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        filterDrawerRef.current && 
        !filterDrawerRef.current.contains(event.target as Node) &&
        !(event.target as HTMLElement).closest('#filter-sort-button')
      ) {
        closeFilterDrawer();
      }
    }
    
    // Add event listener when drawer is open
    if (isFilterDrawerOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isFilterDrawerOpen]);
  
  // Toggle drawer section - keep exactly the same
  const toggleDrawerSection = (section: string) => {
    setExpandedDrawerSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Get category IDs for type filtering - keep exactly the same
  const facetedGemsCategory = categories.find(cat => cat.name === 'Faceted Gems');
  const jewelryCategory = categories.find(cat => cat.name === 'Jewelry');
  const roughGemsCategory = categories.find(cat => cat.name === 'Rough Gems');

  // Sort options with translations - keep exactly the same
  const sortOptions: SortOption[] = [
    { label: translations.newest, value: 'newest' },
    { label: translations.price_low_to_high, value: 'price_asc' },
    { label: translations.price_high_to_low, value: 'price_desc' },
    { label: translations.name_a_to_z, value: 'name_asc' },
    { label: translations.name_z_to_a, value: 'name_desc' },
    { label: translations.weight_low_to_high, value: 'weight_asc' },
    { label: translations.weight_high_to_low, value: 'weight_desc' },
    ...(facetedGemsCategory ? [{ label: translations.type_faceted, value: `type_${facetedGemsCategory._id}` }] : []),
    ...(jewelryCategory ? [{ label: translations.type_jewelry, value: `type_${jewelryCategory._id}` }] : []),
    ...(roughGemsCategory ? [{ label: translations.type_rough, value: `type_${roughGemsCategory._id}` }] : []),
  ];

  // Check if we're on mobile - keep exactly the same
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // Initialize URL params on mount - simplified since data is pre-loaded
  useEffect(() => {
    // Initialize expanded state
    const expanded: Record<string, boolean> = {};
    initialCategories.forEach((category: Category) => {
      expanded[category._id] = false;
    });
    setExpandedCategories(expanded);

    // Handle navigation from header/mobile menu links (single selection mode)
    const categoryParam = searchParams.get('category');
    const subcategoryParam = searchParams.get('subcategory');
    
    if (categoryParam || subcategoryParam) {
      // Navigation context - replace all selections (single selection mode)
      let newSelectedCategories: string[] = [];
      let newSelectedSubcategories: string[] = [];
      let newExpandedCategories = { ...expanded };

      // Handle category URL parameter
      if (categoryParam) {
        const matchedCategory = initialCategories.find((cat: Category) =>
          cat.name.toLowerCase() === categoryParam.toLowerCase()
        );
        if (matchedCategory) {
          newSelectedCategories = [matchedCategory._id]; // Replace, don't add
          if (matchedCategory.subcategories.length > 0) {
            newExpandedCategories[matchedCategory._id] = true;
          }
        }
      }

      // Handle subcategory URL parameter
      if (subcategoryParam) {
        let matchedSubcategory = null;
        let parentCategory = null;

        for (const category of initialCategories) {
          const subcategory = category.subcategories.find((sub: Subcategory) =>
            sub.name.toLowerCase() === subcategoryParam.toLowerCase()
          );
          if (subcategory) {
            matchedSubcategory = subcategory;
            parentCategory = category;
            break;
          }
        }

        if (matchedSubcategory && parentCategory) {
          newSelectedCategories = [parentCategory._id]; // Replace, don't add
          newSelectedSubcategories = [matchedSubcategory._id]; // Replace, don't add
          newExpandedCategories[parentCategory._id] = true;
        }
      }

      // Apply the single selections from navigation
      setSelectedCategories(newSelectedCategories);
      setSelectedSubcategories(newSelectedSubcategories);
      setExpandedCategories(newExpandedCategories);
    } else {
      // No category/subcategory URL parameters - reset category filters but preserve search query
      setSelectedCategories([]);
      setSelectedSubcategories([]);
      setSelectedShape(null);
      setPriceRange([0, 10000]);
      // Preserve search query from URL parameter instead of resetting it
      const searchParam = searchParams.get('search') || '';
      setSearchQuery(searchParam);
      setSortOption('newest');
      setExpandedCategories(expanded);
    }
  }, [searchParams, initialCategories]);

  // Helper function to get category ID
  const getCategoryId = (category: string | CategoryObj): string => {
    return typeof category === 'string' ? category : category._id;
  };

  // Modify category selection to support multiple
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
    
    // When a category is deselected, remove its subcategories
    if (!selectedCategories.includes(categoryId)) {
      const categorySubcategoryIds = initialCategories
        .find(cat => cat._id === categoryId)?.subcategories
        .map(sub => sub._id) || [];
      
      setSelectedSubcategories(prev => 
        prev.filter(subId => !categorySubcategoryIds.includes(subId))
      );
    }
  };

  // Modify subcategory selection to support multiple
  const handleSubcategorySelect = (subcategoryId: string) => {
    let parentCategoryId = '';
    
    for (const category of categories) {
      const subcategory = category.subcategories.find((sub: Subcategory) => sub._id === subcategoryId);
      if (subcategory) {
        parentCategoryId = category._id;
        break;
      }
    }

    // Add/remove subcategory
    setSelectedSubcategories(prev => 
      prev.includes(subcategoryId) 
        ? prev.filter(id => id !== subcategoryId)
        : [...prev, subcategoryId]
    );

    // Ensure parent category is selected if a subcategory is selected
    if (!selectedCategories.includes(parentCategoryId)) {
      setSelectedCategories(prev => [...prev, parentCategoryId]);
    }
  };

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Modify getFilteredProducts to work with multiple selections
  const getFilteredProducts = () => {
    let filtered = [...products];
    
    // Filter by categories (multiple)
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(product => {
        let productCategoryId = getCategoryId(product.category);
        return selectedCategories.includes(productCategoryId);
      });
    }
    
    // Filter by subcategories (multiple)
    if (selectedSubcategories.length > 0) {
      filtered = filtered.filter(product => {
        // Find matching subcategories by name
        return selectedSubcategories.some(selectedSubId => {
          // Find the subcategory name
          let subcategoryName = '';
          for (const category of categories) {
            const subcategory = category.subcategories.find((sub: Subcategory) => sub._id === selectedSubId);
            if (subcategory) {
              subcategoryName = subcategory.name;
              break;
            }
          }

          // Check if product name includes subcategory name
          return subcategoryName && product.name.toLowerCase().includes(subcategoryName.toLowerCase());
        });
      });
    }
    
    // Filter by shape
    if (selectedShape) {
      filtered = filtered.filter(product => 
        product.shape === selectedShape
      );
    }
    
    // Filter by price
    filtered = filtered.filter(
      product => product.price >= priceRange[0] && product.price <= priceRange[1]
    );
    
    // Filter by search query (direct, no debounce)
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        product => 
          product.name.toLowerCase().includes(query) || 
          (product.description && product.description.en && 
           product.description.en.toLowerCase().includes(query)) ||
          (product.description && product.description.fr && 
           product.description.fr.toLowerCase().includes(query)) ||
          (product.description && product.description.it && 
           product.description.it.toLowerCase().includes(query))
      );
    }

    // Filter by type
    if (sortOption.startsWith('type_')) {
      const categoryId = sortOption.replace('type_', '');
      filtered = filtered.filter(product => {
        let productCategoryId = product.category;
        if (typeof product.category === 'object' && product.category !== null && '_id' in product.category) {
          productCategoryId = product.category._id;
        }
        return productCategoryId === categoryId;
      });
    }
    
    // Sort products - EXACT SAME LOGIC
    if (sortOption.startsWith('type_')) {
      filtered.sort((a, b) => b._id.localeCompare(a._id));
    } else {
      switch (sortOption) {
        case 'price_asc':
          filtered.sort((a, b) => a.price - b.price);
          break;
        case 'price_desc':
          filtered.sort((a, b) => b.price - a.price);
          break;
        case 'name_asc':
          filtered.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case 'name_desc':
          filtered.sort((a, b) => b.name.localeCompare(a.name));
          break;
        case 'weight_asc':
          filtered.sort((a, b) => (a.weight || 0) - (b.weight || 0));
          break;
        case 'weight_desc':
          filtered.sort((a, b) => (b.weight || 0) - (a.weight || 0));
          break;
        case 'newest':
        default:
          filtered.sort((a, b) => b._id.localeCompare(a._id));
          break;
      }
    }
    
    return filtered;
  };

  // Get remaining products (total - filtered)
  const getRemainingProducts = () => {
    const filteredProducts = getFilteredProducts();
    const filteredIds = new Set(filteredProducts.map(product => product._id));
    return products.filter(product => !filteredIds.has(product._id));
  };

  // Simple handlers without memo
  const resetFilters = () => {
    setSelectedCategories([]);
    setSelectedSubcategories([]);
    setSelectedShape(null);
    setPriceRange([0, 10000]);
    setSearchQuery('');
    setSortOption('newest');
  };

  // PERFORMANCE OPTIMIZATION: Enhanced subcategory counts calculation with proper memoization
  const subcategoryCounts = useMemo(() => {
    const counts: Record<string, number> = {};

    // Early return if no data
    if (!categories.length || !products.length) {
      return counts;
    }

    categories.forEach(category => {
      category.subcategories.forEach(subcategory => {
        // Filter products that match this subcategory
        let subcategoryProducts = products.filter(product => {
          // First filter by category if one is selected
          if (selectedCategories.length > 0) {
            let productCategoryId = getCategoryId(product.category);
            if (!selectedCategories.includes(productCategoryId)) {
              return false;
            }
          }

          // Filter by subcategory name (product name contains subcategory name)
          return product.name.toLowerCase().includes(subcategory.name.toLowerCase());
        });

        // Apply additional filters (price range and search query) but not subcategory filter
        subcategoryProducts = subcategoryProducts.filter(product => {
          // Price filter
          if (product.price < priceRange[0] || product.price > priceRange[1]) {
            return false;
          }

          // Search query filter
          if (searchQuery) {
            const query = searchQuery.toLowerCase();
            const matchesSearch =
              product.name.toLowerCase().includes(query) ||
              (product.description && product.description.en &&
               product.description.en.toLowerCase().includes(query)) ||
              (product.description && product.description.fr &&
               product.description.fr.toLowerCase().includes(query)) ||
              (product.description && product.description.it &&
               product.description.it.toLowerCase().includes(query));
            if (!matchesSearch) {
              return false;
            }
          }

          return true;
        });

        counts[subcategory._id] = subcategoryProducts.length;
      });
    });

    return counts;
  }, [categories, products, selectedCategories, priceRange, searchQuery]);

  // Find category name by ID
  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat._id === categoryId);
    return category ? category.name : '';
  };

  // Find subcategory name by ID
  const getSubcategoryName = (categoryId: string | undefined, subcategoryId: string) => {
    if (!categoryId) return '';
    const category = categories.find(cat => cat._id === categoryId);
    if (!category) return '';
    
    const subcategory = category.subcategories.find(sub => sub._id === subcategoryId);
    return subcategory ? subcategory.name : '';
  };

  // Get the correct image URL based on available information
  const getProductImageUrl = (product: Product): string => {
    if (product.imageUrl) {
      // If using CloudFront CDN
      if (process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN) {
        return `${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${product.imageUrl}`;
      }
      // If imageUrl is a full path
      else if (product.imageUrl.startsWith('http') || product.imageUrl.startsWith('/')) {
        return product.imageUrl;
      }
    }
    
    // Fallback to images array if available
    if (product.images && product.images.length > 0) {
      return product.images[0];
    }
    
    // Fallback to a default gem image
    return '/images/sphene.jpg'; // Using an existing gem image as placeholder
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-center p-10 bg-red-50 rounded-lg">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-red-500">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      {/* YOUR COMPLETE UI STARTS HERE - EXACT SAME STRUCTURE */}
      <div id="shop-page-container" className="container mx-auto px-4">
        {/* Mobile filter toggle (now hidden) */}
        <div id="mobile-filter-toggle" className="hidden">
          <button 
            className="w-full py-3 px-4 bg-gray-100 rounded-md flex justify-between items-center"
          >
            <span className="font-medium flex items-center">
              <Filter size={18} className="mr-2" />
              {translations.filters_and_sort}
            </span>
          </button>
        </div>
        
        <div id="shop-content-wrapper" className="flex flex-col md:flex-row gap-6 sm:gap-8">
          {/* Sidebar Filters - Replaced with NewSidebar */}
          <div id="new-shop-filters-sidebar" className="hidden md:block md:w-1/4 lg:w-1/5">
            <NewSidebar
              categories={categories}
              selectedCategories={selectedCategories}
              selectedSubcategories={selectedSubcategories}
              expandedCategories={expandedCategories}
              priceRange={priceRange}
              searchQuery={searchQuery}
              onCategorySelect={handleCategorySelect}
              onSubcategorySelect={handleSubcategorySelect}
              onToggleCategory={toggleCategory}
              onPriceChange={(min, max) => setPriceRange([min, max])}
              onSearchChange={setSearchQuery}
              onResetFilters={resetFilters}
              subcategoryCounts={subcategoryCounts}
            />
          </div>
          
          {/* Main Content */}
          <div id="shop-main-content" className="md:w-3/4 lg:w-4/5">
            {/* Sort and Results Info */}
            <div id="sortBy-sort-count" className="flex justify-between items-center mb-6 border-b border-t" style={{ height: '51px', padding: '13px' }}>
              <div 
                id="filter-sort-button"
                onClick={() => setIsFilterDrawerOpen(true)}
                className="font-semibold flex items-center text-[#3A3A3A] cursor-pointer hover:text-[#000000]"
                style={{ 
                  fontFamily: 'var(--font-dosis), sans-serif',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.08em',
                  textTransform: 'capitalize'
                }}
              >
                <SlidersHorizontal size={18} className="mr-2" />
                Filter and sort
              </div>
            </div>
            
            {/* Active Filters */}
            {(selectedCategories.length > 0 || selectedSubcategories.length > 0 || searchQuery || priceRange[0] > 0 || priceRange[1] < 10000 || sortOption.startsWith('type_') || selectedShape) && (
              <div id="shop-active-filters" className="mb-6 flex flex-wrap gap-2 items-center">
                <span className="text-sm text-gray-600">{translations.active_filters}</span>
                
                {selectedCategories.map(categoryId => (
                  <span 
                    key={categoryId} 
                    id={`active-filter-category-${categoryId}`} 
                    className="text-sm bg-transparent text-black px-3 py-1 rounded-full flex items-center"
                  >
                    {getCategoryName(categoryId)}
                    <button 
                      onClick={() => handleCategorySelect(categoryId)} 
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      <X size={14} />
                    </button>
                  </span>
                ))}
                
                {selectedSubcategories.map(subcategoryId => {
                  // Find the parent category for this subcategory
                  const parentCategory = categories.find(cat => 
                    cat.subcategories.some(sub => sub._id === subcategoryId)
                  );
                  
                  return (
                    <span 
                      key={subcategoryId} 
                      id={`active-filter-subcategory-${subcategoryId}`} 
                      className="text-sm bg-transparent text-black px-3 py-1 rounded-full flex items-center"
                    >
                      {getSubcategoryName(parentCategory?._id, subcategoryId)}
                      <button 
                        onClick={() => handleSubcategorySelect(subcategoryId)} 
                        className="ml-1 text-blue-600 hover:text-blue-800"
                      >
                        <X size={14} />
                      </button>
                    </span>
                  );
                })}
                
                {selectedShape && (
                  <span id="active-filter-shape" className="text-sm bg-transparent text-black px-3 py-1 rounded-full flex items-center">
                    Shape: {selectedShape}
                    <button onClick={() => setSelectedShape(null)} className="ml-1 text-blue-600 hover:text-blue-800">
                      <X size={14} />
                    </button>
                  </span>
                )}
                
                {searchQuery && (
                  <span id="active-filter-search" className="text-sm bg-transparent text-black px-3 py-1 rounded-full flex items-center">
                    {translations.search_filter_label} {searchQuery}
                    <button onClick={() => setSearchQuery('')} className="ml-1 text-blue-600 hover:text-blue-800">
                      <X size={14} />
                    </button>
                  </span>
                )}
                
                {(priceRange[0] > 0 || priceRange[1] < 10000) && (
                  <span id="active-filter-price" className="text-sm bg-transparent text-black px-3 py-1 rounded-full flex items-center">
                    {translations.price_filter_label} ${priceRange[0]} - ${priceRange[1]}
                    <button onClick={() => setPriceRange([0, 10000])} className="ml-1 text-blue-600 hover:text-blue-800">
                      <X size={14} />
                    </button>
                  </span>
                )}

                {sortOption.startsWith('type_') && (
                  <span id="active-filter-type" className="text-sm bg-transparent text-black px-3 py-1 rounded-full flex items-center">
                    {(() => {
                      const categoryId = sortOption.replace('type_', '');
                      const category = categories.find(cat => cat._id === categoryId);
                      return category ? category.name : 'Product Type';
                    })()}
                    <button onClick={() => setSortOption('newest')} className="ml-1 text-blue-600 hover:text-blue-800">
                      <X size={14} />
                    </button>
                  </span>
                )}

                <button
                  id="clear-all-filters-btn"
                  onClick={resetFilters}
                  className="text-sm text-black hover:text-blue-800 hover:underline ml-auto"
                >
                  {translations.clear_all}
                </button>
              </div>
            )}
            
            {/* Products Grid */}
            {getFilteredProducts().length === 0 ? (
              <div id="no-products-found" className="text-center py-16 bg-[#f8f8f8] rounded-lg border border-[#D1C29B]">
                <div className="mx-auto w-20 h-20 mb-4 flex items-center justify-center rounded-full bg-white border border-[#D1C29B]">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-[#D1C29B]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2">{translations.no_products_found}</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {translations.no_products_description}
                </p>
                <button
                  id="reset-filters-empty-state-btn"
                  onClick={resetFilters}
                  className="inline-block px-6 py-3 bg-gradient-to-tl from-[#51575F] to-[#1F2937] text-white rounded-md hover:bg-gradient-to-tl hover:from-[#6B7280] hover:to-[#374151] active:bg-gradient-to-tl active:from-[#4B5563] active:to-[#111827] hover:shadow-lg active:shadow-sm active:scale-95 transition-all duration-200 font-medium"
                >
                  {translations.reset_filters_button}
                </button>
              </div>
            ) : (
              <div id="products-container">
                
                {/* Product grid with same layout as featured products - PERFORMANCE OPTIMIZED */}
                <div id="products-grid" className="grid grid-cols-4 sm:grid-cols-4 lg:grid-cols-8 gap-4 sm:gap-5 md:gap-6">
                  {getFilteredProducts().map((product, index) => (
                    <div key={product._id} id={`product-item-${product._id}`} className="col-span-2">
                      <ProductCard
                        className="col-span-1"
                        imageSrc={getProductImageUrl(product)}
                        productName={product.name}
                        regularPrice={product.price}
                        salePrice={product.price}
                        weight={product.weight || 0}
                        alt={product.name}
                        category={typeof product.category === 'object' ? product.category._id : product.category}
                        productId={product._id} // Pass real product ID
                        inventory={product.inventory}
                        href={`/product/${product._id}`}
                        priority={index < 8}
                        loading={index < 16 ? 'eager' : 'lazy'}
                        fetchPriority={index < 8 ? 'high' : 'auto'}
                      />
                    </div>
                  ))}
                </div>

                {/* All Other Products Section - Show only if there are filters applied and remaining products */}
                {(selectedCategories.length > 0 || selectedSubcategories.length > 0 || searchQuery || priceRange[0] > 0 || priceRange[1] < 10000 || sortOption.startsWith('type_') || selectedShape) && getRemainingProducts().length > 0 && (
                  <div id="remaining-products-container" className="mt-12">
                    {/* Heading for remaining products */}
                    <div id="remaining-products-heading" className="flex flex-col items-center mb-6 sm:mb-8 md:mb-10">
                      <h2 
                        className="text-black text-center mb-2 sm:mb-3 capitalize"
                        style={{ 
                          fontFamily: 'var(--font-dosis), sans-serif',
                          fontWeight: '500',
                          fontSize: '20px',
                          lineHeight: '28px',
                          letterSpacing: '2px'
                        }}
                      >
                        All Other Products from Shop
                      </h2>
                    </div>
                  
                    {/* Remaining products grid */}
                    <div id="remaining-products-grid" className="grid grid-cols-4 sm:grid-cols-4 lg:grid-cols-8 gap-4 sm:gap-5 md:gap-6">
                      {getRemainingProducts().map((product, index) => (
                        <div key={product._id} id={`remaining-product-item-${product._id}`} className="col-span-2">
                          <ProductCard
                            className="col-span-1"
                            imageSrc={getProductImageUrl(product)}
                            productName={product.name}
                            regularPrice={product.price}
                            salePrice={product.price}
                            weight={product.weight || 0}
                            alt={product.name}
                            category={typeof product.category === 'object' ? product.category._id : product.category}
                            productId={product._id} // Pass real product ID
                            inventory={product.inventory}
                            href={`/product/${product._id}`}
                            priority={false}
                            loading="lazy"
                            fetchPriority="auto"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Filter Drawer - YOUR EXACT SAME BEAUTIFUL MOBILE DRAWER */}
        {(isFilterDrawerOpen || isFilterDrawerClosing) && (
          <div 
            id="filter-drawer-overlay"
            className="fixed inset-0 z-50 transition-opacity"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            onClick={() => closeFilterDrawer()}
          >
            <div 
              id="filter-drawer-container"
              ref={filterDrawerRef}
              className={`fixed top-0 right-0 h-full shadow-xl transform transition-transform duration-300 ease-in-out overflow-y-auto ${isFilterDrawerClosing ? 'animate-slide-out-right' : 'animate-slide-in-right'}`}
              style={{ 
                width: isMobile ? '75%' : '384px', 
                maxWidth: '100vw', 
                backgroundColor: 'rgba(255, 255, 255, 0.01)', 
                backdropFilter: 'blur(5px)' 
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div id="filter-drawer-header" className="relative flex justify-center items-center p-4 border-b">
                <div id="filter-drawer-header-content" className="flex flex-col text-center">
                  {/* Update header text color */}
                  <h2 
                    className="text-white"
                    style={{ 
                      fontFamily: 'var(--font-dosis), sans-serif',
                      fontWeight: '400',
                      fontSize: '21px',
                      lineHeight: '20px',
                      letterSpacing: '0.07em',
                      marginBottom: '12px'
                    }}
                  >
                    Filter and sort
                  </h2>
                  <p 
                    className="text-gray-200"
                    style={{ 
                      fontFamily: 'var(--font-dosis), sans-serif',
                      fontWeight: '400',
                      fontSize: '15px',
                      lineHeight: '20px',
                      letterSpacing: '0.10em',
                      textTransform: 'capitalize'
                    }}
                  >
                    {getFilteredProducts().length} {getFilteredProducts().length === 1 ? translations.product_singular : translations.products_count}
                  </p>
                </div>
                <button 
                  className="absolute right-4 text-white hover:text-gray-200"
                  onClick={() => closeFilterDrawer()}
                  aria-label="Close filter drawer"
                >
                  <X size={24} />
                </button>
              </div>
              
              {/* Filter Content */}
              <div id="filter-drawer-content" className="px-4 pb-4" style={{ paddingTop: '38px' }}>
                {/* Gemstone Variety (Categories) */}
                <div id="filter-drawer-gemstone-section" style={{ marginBottom: '16px' }}>
                  <div 
                    id="filter-drawer-gemstone-header"
                    className="flex justify-between items-center py-3 cursor-pointer"
                    onClick={() => toggleDrawerSection('gemstoneVariety')}
                  >
                    <h3 
                      className="text-white"
                      style={{
                        fontFamily: 'var(--font-dosis), sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.07em'
                      }}
                    >
                      Gemstone Variety
                    </h3>
                    {expandedDrawerSections.gemstoneVariety ? (
                      <ChevronDown size={20} className="text-white" />
                    ) : (
                      <svg width="6" height="12" viewBox="0 0 6 12" fill="white" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 6L0.714495 12L0.714355 11L3.36756 6L0.714495 1L0.714495 0L6 6Z" fill="white"/>
                      </svg>
                    )}
                  </div>
                  
                  {/* Category Items */}
                  {expandedDrawerSections.gemstoneVariety && (
                    <div id="filter-drawer-category-items" className="mt-2 space-y-2">
                      {categories.map((category) => (
                        <div key={category._id} id={`filter-drawer-category-${category._id}`} className="py-2 text-white">
                          <div 
                            id={`filter-drawer-category-main-${category._id}`}
                            className="flex items-center justify-between cursor-pointer text-white"
                            onClick={() => toggleCategory(category._id)}
                          >
                            <div id={`filter-drawer-category-left-${category._id}`} className="flex items-center text-white">
                              <div id={`filter-drawer-category-checkbox-container-${category._id}`} className="flex-shrink-0 w-7 h-6 relative mr-2 flex items-center justify-center">
                                {selectedCategories.includes(category._id) ? (
                                  <Image
                                    className="check-icon cursor-pointer"
                                    width={18} 
                                    height={18} 
                                    src="/images/check0.svg"
                                    alt="Selected"
                                    loading="lazy"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleCategorySelect(category._id);
                                    }}
                                  />
                                ) : (
                                  <div 
                                    id={`filter-drawer-category-checkbox-${category._id}`}
                                    className="rounded-sm border border-white w-4 h-4 bg-transparent cursor-pointer"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleCategorySelect(category._id);
                                    }}
                                  ></div>
                                )}
                              </div>
                              <span 
                                className="text-white"
                                style={{
                                  fontFamily: 'var(--font-dosis), sans-serif',
                                  fontWeight: '400',
                                  fontSize: '14px',
                                  lineHeight: '20px',
                                  letterSpacing: '0.06em'
                                }}
                              >
                                {translateCategoryName(category.name)}
                              </span>
                            </div>
                            {expandedCategories[category._id] ? (
                              <ChevronUp size={16} className="text-white" />
                            ) : (
                              <ChevronDown size={16} className="text-white" />
                            )}
                          </div>
                          
                          {/* Subcategories */}
                          {expandedCategories[category._id] && (
                            <div id={`filter-drawer-subcategories-${category._id}`} className="pl-8 mt-2 space-y-2">
                              {category.subcategories.map(subcategory => {
                                const counts = subcategoryCounts;
                                return (
                                  <div key={subcategory._id} id={`filter-drawer-subcategory-${subcategory._id}`} className="flex items-center py-1">
                                    <div id={`filter-drawer-subcategory-checkbox-container-${subcategory._id}`} className="flex-shrink-0 w-7 h-6 relative mr-2 flex items-center justify-center">
                                      {selectedSubcategories.includes(subcategory._id) ? (
                                        <Image
                                          className="check-icon cursor-pointer"
                                          width={18} 
                                          height={18} 
                                          src="/images/check0.svg"
                                          alt="Selected"
                                          loading="lazy"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleSubcategorySelect(subcategory._id);
                                          }}
                                        />
                                      ) : (
                                        <div 
                                          id={`filter-drawer-subcategory-checkbox-${subcategory._id}`}
                                          className="rounded-sm border border-white w-4 h-4 bg-transparent cursor-pointer"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleSubcategorySelect(subcategory._id);
                                          }}
                                        ></div>
                                      )}
                                    </div>
                                    <span 
                                      className="text-white text-opacity-80 cursor-pointer"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleSubcategorySelect(subcategory._id);
                                      }}
                                      style={{
                                        fontFamily: 'var(--font-dosis), sans-serif',
                                        fontWeight: '400',
                                        fontSize: '13px',
                                        lineHeight: '20px',
                                        letterSpacing: '0.10em'
                                      }}
                                    >
                                      {translateSubcategoryName(category.name, subcategory.name)} ({counts[subcategory._id] || 0})
                                    </span>
                                  </div>
                                );
                              })}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* Shape */}
                <div id="filter-drawer-shape-section" className="mb-6">
                  <div 
                    id="filter-drawer-shape-header"
                    className="flex justify-between items-center py-3 cursor-pointer"
                    onClick={() => toggleDrawerSection('shape')}
                  >
                    <h3 
                      className="text-white"
                      style={{
                        fontFamily: 'var(--font-dosis), sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.07em'
                      }}
                    >
                      Shape
                    </h3>
                    {expandedDrawerSections.shape ? (
                      <ChevronDown size={20} className="text-white" />
                    ) : (
                      <svg width="6" height="12" viewBox="0 0 6 12" fill="white" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 6L0.714495 12L0.714355 11L3.36756 6L0.714495 1L0.714495 0L6 6Z" fill="white"/>
                      </svg>
                    )}
                  </div>
                  {expandedDrawerSections.shape && (
                    <div id="filter-drawer-shape-content" className="mt-4">
                      <div id="filter-drawer-shape-grid" className="grid grid-cols-3 gap-2">
                        {[
                          { name: 'Oval', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><ellipse cx="12" cy="12" rx="8" ry="5" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Round', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><circle cx="12" cy="12" r="7" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Pear', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><path d="M12,5 C15,5 18,8 18,12 C18,16 15,19 12,19 C9,19 6,16 6,12 C6,10 7,7 12,5 Z" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Emerald', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><rect x="6" y="8" width="12" height="8" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Marquise', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><ellipse cx="12" cy="12" rx="8" ry="4" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Radiant', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><rect x="6" y="7" width="12" height="10" rx="2" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Square', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><rect x="7" y="7" width="10" height="10" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Heart', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><path d="M12,18 C12,18 5,14 5,9 C5,6.5 7,5 9,5 C10.5,5 12,6 12,7.5 C12,6 13.5,5 15,5 C17,5 19,6.5 19,9 C19,14 12,18 12,18 Z" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Cushion', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><rect x="6" y="6" width="12" height="12" rx="3" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Trilliant', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><polygon points="12,5 19,19 5,19" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Octagonal', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><polygon points="9,5 15,5 19,9 19,15 15,19 9,19 5,15 5,9" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> },
                          { name: 'Triangular', svg: <svg viewBox="0 0 24 24" className="w-8 h-8"><polygon points="12,5 19,19 5,19" stroke="currentColor" fill="none" strokeWidth="1.5"/></svg> }
                        ].map((shape) => (
                          <div 
                            key={shape.name}
                            id={`filter-drawer-shape-${shape.name.toLowerCase()}`}
                            onClick={() => setSelectedShape(selectedShape === shape.name ? null : shape.name)}
                            className={`border rounded-md p-2 flex flex-col items-center justify-center cursor-pointer transition-all ${
                              selectedShape === shape.name ? 'border-2 border-[#ff00d6]' : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <div id={`filter-drawer-shape-icon-${shape.name.toLowerCase()}`} className="mb-1 text-gray-700">{shape.svg}</div>
                            <div 
                              id={`filter-drawer-shape-name-${shape.name.toLowerCase()}`} 
                              className="text-xs text-center text-white text-opacity-80"
                              style={{ 
                                fontFamily: 'var(--font-dosis), sans-serif',
                                letterSpacing: '0.08em'
                              }}
                            >
                              {shape.name}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Clear All Filters Button */}
                <div id="filter-drawer-clear-section" className="mt-8 mb-4">
                  {/* Update button text color */}
                  <button
                    id="filter-drawer-clear-button"
                    onClick={resetFilters}
                    className="w-full py-2.5 bg-gray-700 text-white rounded-md hover:bg-gray-600 active:bg-gray-800 active:scale-95 transition-all duration-100 ease-in-out"
                    style={{
                      fontFamily: 'var(--font-dosis), sans-serif',
                      fontWeight: '500',
                      letterSpacing: '0.07em'
                    }}
                  >
                    Clear all filters
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}