import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';
import { Product } from '@/models/Product';
import { Category } from '@/models/Category';
import { SystemLog } from '@/models/SystemLog';
import { logActions } from '@/lib/logActivity';

/**
 * Create a new product in MongoDB
 * 
 * POST /api/products
 */
export async function POST(request: Request) {
  try {
    // Parse the request body
    const productData = await request.json();
    
    // Extract user info for logging
    const { userId, userName, ...productDetailsToSave } = productData;
    
    console.log('--- PRODUCT DETAILS TO SAVE ---');
    console.log(JSON.stringify(productDetailsToSave, null, 2));
    console.log('--- END PRODUCT DETAILS TO SAVE ---');
    
    // Validate required fields
    if (!productDetailsToSave.name || !productDetailsToSave.description?.en || !productDetailsToSave.price || !productDetailsToSave.category) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }
    
    // Ensure inventory is initialized with default values if not provided
    if (!productDetailsToSave.inventory) {
      productDetailsToSave.inventory = {
        inStock: 0,
        lowStockThreshold: 5
      };
    }
    
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Create the product using the imported model
    const newProduct = new Product(productDetailsToSave);
    await newProduct.save();
    
    // Log the product creation if user info is provided
    if (userId && userName) {
      await SystemLog.create({
        action: 'create',
        resource: 'product',
        userId,
        userName,
        details: `Created product: ${productDetailsToSave.name}`,
        status: 'success'
      });
    }
    
    return NextResponse.json({
      success: true,
      product: newProduct
    });
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create product',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Get all products from MongoDB
 * 
 * GET /api/products
 */
export async function GET(request: NextRequest) {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }
    
    // Parse query parameters
    const url = new URL(request.url);
    const categoryId = url.searchParams.get('category');
    const subcategoryId = url.searchParams.get('subcategory');
    const search = url.searchParams.get('search');
    const isFeatured = url.searchParams.get('featured');
    const isLatest = url.searchParams.get('latest');
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const fields = url.searchParams.get('fields'); // For limiting returned fields
    
    // Build query
    const query: any = {};
    
    if (categoryId) {
      query.category = categoryId;
    }
    
    if (subcategoryId) {
      query.subcategory = subcategoryId;
    }
    
    // Filter by featured or latest if provided
    if (isFeatured === 'true') {
      query.isFeatured = true;
    }
    
    if (isLatest === 'true') {
      query.isLatest = true;
    }
    
    // Add search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Build field selection for optimization
    let fieldSelection = '';
    if (fields === 'homepage') {
      // Only select essential fields for homepage display
      fieldSelection = 'name price imageUrl videoUrl weight shape isFeatured isLatest createdAt category inventory';
    }
    
    // Fetch products - optimize for homepage by skipping category population when using fields=homepage
    let products;
    if (fields === 'homepage') {
      // For homepage, skip expensive category population to prevent timeouts
      products = await Product.find(query)
        .select(fieldSelection)
        .limit(limit)
        .sort({ createdAt: -1 });
    } else {
      // For other pages, include category population
      products = await Product.find(query)
        .populate('category', 'name subcategories')
        .select(fieldSelection)
        .limit(limit)
        .sort({ createdAt: -1 });
    }

    // Transform products to include subcategory information
    const transformedProducts = products.map(product => {
      const productObj = product.toObject();

      // If product has a subcategory and category is populated, find the subcategory details
      if (productObj.subcategory && productObj.category && productObj.category.subcategories) {
        const subcategoryId = productObj.subcategory.toString();
        const subcategory = productObj.category.subcategories.find(
          (sub: any) => sub._id.toString() === subcategoryId
        );

        if (subcategory) {
          // Replace subcategory ID with subcategory object
          productObj.subcategory = {
            _id: subcategory._id,
            name: subcategory.name
          };
        }
      }

      return productObj;
    });

    // Return the products wrapped in a success/products structure
    return NextResponse.json({
      success: true,
      products: transformedProducts
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch products',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 