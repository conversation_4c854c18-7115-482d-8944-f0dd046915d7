import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { saveCartToDatabase } from '@/services/cartSyncService';

// Define Product type
export type Product = {
  _id: string;
  name: string;
  description: {
    en: string;
    fr?: string;
    it?: string;
  } | string;
  price: number;
  category: {
    _id: string;
    name: string;
  } | string;
  imageUrl?: string;
  videoUrl?: string;
  weight?: number;
  createdAt: string;
  inventory?: {
    inStock: number;
    lowStockThreshold: number;
  };
}

// Define CartItem type
export type CartItem = {
  product: Product;
  quantity: number;
}

// Define CartStore type
type CartStore = {
  items: CartItem[];
  isOpen: boolean;
  lastSynced: number;
  
  // Actions
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
  getItemsCount: () => number;
  getTotal: () => number;
  syncWithDatabase: () => Promise<void>;
}

// Debounce function to avoid too many database calls
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Create the store
export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      isOpen: false,
      lastSynced: 0,
      
      // Add an item to the cart
      addItem: (product, quantity = 1) => {
        set((state) => {
          const existingItem = state.items.find(item => item.product._id === product._id);

          let newState;
          if (existingItem) {
            // If item exists, update quantity and move to top
            const updatedItem = { ...existingItem, quantity: existingItem.quantity + quantity };
            const otherItems = state.items.filter(item => item.product._id !== product._id);
            newState = {
              items: [updatedItem, ...otherItems]
            };
          } else {
            // If item doesn't exist, add it at the beginning (top of the list)
            newState = {
              items: [{ product, quantity }, ...state.items]
            };
          }

          return newState;
        });
        
        // Sync immediately without debounce to ensure cart is saved
        setTimeout(() => {
          get().syncWithDatabase();
        }, 100);
      },
      
      // Remove an item from the cart
      removeItem: (productId) => {
        set((state) => {
          const newState = {
            items: state.items.filter(item => item.product._id !== productId)
          };
          
          // Schedule database sync with shorter delay
          const debouncedSync = debounce(() => {
            get().syncWithDatabase();
          }, 500); // Reduced from 2000ms to 500ms
          debouncedSync();
          
          return newState;
        });
      },
      
      // Update an item's quantity
      updateQuantity: (productId, quantity) => {
        set((state) => {
          let newState;
          if (quantity <= 0) {
            // If quantity is zero or negative, remove the item
            newState = {
              items: state.items.filter(item => item.product._id !== productId)
            };
          } else {
            // Otherwise update the quantity
            newState = {
              items: state.items.map(item => 
                item.product._id === productId
                  ? { ...item, quantity }
                  : item
              )
            };
          }
          
          // Schedule database sync with shorter delay
          const debouncedSync = debounce(() => {
            get().syncWithDatabase();
          }, 500); // Reduced from 2000ms to 500ms
          debouncedSync();
          
          return newState;
        });
      },
      
      // Clear the cart
      clearCart: () => {
        set({ items: [] });
        
        // Schedule database sync with shorter delay
        const debouncedSync = debounce(() => {
          get().syncWithDatabase();
        }, 500); // Reduced from 2000ms to 500ms
        debouncedSync();
      },
      
      // Open the cart
      openCart: () => set({ isOpen: true }),
      
      // Close the cart - Simply sets isOpen to false
      // The animation is handled in the CartDrawer component
      closeCart: () => set({ isOpen: false }),
      
      // Toggle the cart
      toggleCart: () => set((state) => ({ isOpen: !state.isOpen })),
      
      // Get the number of items in the cart
      getItemsCount: () => {
        return get().items.reduce((total, item) => total + item.quantity, 0);
      },
      
      // Get the total price of items in the cart
      getTotal: () => {
        return get().items.reduce(
          (total, item) => total + (item.product.price * item.quantity), 
          0
        );
      },
      
      // Sync cart with database
      syncWithDatabase: async () => {
        const items = get().items;
        console.log('[Cart Store] syncWithDatabase called with', items.length, 'items');
        
        if (!items.length) {
          console.log('[Cart Store] No items to sync');
          return;
        }
        
        // Only sync if there are items and if it's been more than 30 seconds since last sync
        const now = Date.now();
        const lastSynced = get().lastSynced;
        
        console.log('[Cart Store] Last synced:', lastSynced ? new Date(lastSynced).toLocaleString() : 'never');
        
        if (now - lastSynced > 30000 || lastSynced === 0) {
          console.log('[Cart Store] Syncing cart now...');
          const success = await saveCartToDatabase(items);
          if (success) {
            console.log('[Cart Store] Sync successful, updating lastSynced timestamp');
            set({ lastSynced: now });
          } else {
            console.log('[Cart Store] Sync failed');
          }
        } else {
          console.log('[Cart Store] Skipping sync, last sync was too recent');
        }
      }
    }),
    {
      name: 'cart-storage', // unique name for localStorage
    }
  )
); 